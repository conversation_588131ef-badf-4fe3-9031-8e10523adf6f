<template>
  <div class="chat-modal-overlay" @click="closeModal">
    <div class="chat-modal" @click.stop>
      <!-- 弹窗头部 -->
      <div class="chat-header">
        <h3>智能助手</h3>
        <button class="close-btn" @click="closeModal">×</button>
      </div>

      <!-- 弹窗内容 -->
      <div class="chat-content">
        <!-- 数字人形象 -->
        <div class="digital-avatar">
          <div class="avatar-image">🤖</div>
          <div class="avatar-name">小智</div>
          <div class="avatar-status">在线为您服务</div>
        </div>

        <!-- 对话消息区域 -->
        <div class="chat-messages" ref="messagesContainer">
          <div 
            v-for="message in messages" 
            :key="message.id" 
            :class="['message', message.type]"
          >
            <div class="message-bubble">
              {{ message.content }}
            </div>
          </div>
        </div>

        <!-- 输入区域 -->
        <div class="chat-input-area">
          <input
            v-model="inputMessage"
            @keyup.enter="sendMessage"
            class="chat-input"
            placeholder="请输入您的问题..."
            :disabled="isTyping"
          />
          <button 
            @click="sendMessage" 
            class="send-btn"
            :disabled="!inputMessage.trim() || isTyping"
          >
            ➤
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ChatModal',
  emits: ['close'],
  data() {
    return {
      inputMessage: '',
      isTyping: false,
      messages: [
        {
          id: 1,
          type: 'assistant',
          content: '您好！我是您的智能助手小智，很高兴为您服务！请问有什么可以帮助您的吗？'
        }
      ]
    }
  },
  methods: {
    closeModal() {
      this.$emit('close')
    },
    async sendMessage() {
      if (!this.inputMessage.trim() || this.isTyping) return

      // 添加用户消息
      const userMessage = {
        id: Date.now(),
        type: 'user',
        content: this.inputMessage.trim()
      }
      this.messages.push(userMessage)

      // 清空输入框
      const userInput = this.inputMessage
      this.inputMessage = ''
      this.isTyping = true

      // 滚动到底部
      this.$nextTick(() => {
        this.scrollToBottom()
      })

      // 模拟数字人思考时间
      setTimeout(() => {
        // 添加数字人回复
        const assistantMessage = {
          id: Date.now() + 1,
          type: 'assistant',
          content: '感谢您的观看！'
        }
        this.messages.push(assistantMessage)
        this.isTyping = false

        // 滚动到底部
        this.$nextTick(() => {
          this.scrollToBottom()
        })
      }, 1000)
    },
    scrollToBottom() {
      const container = this.$refs.messagesContainer
      if (container) {
        container.scrollTop = container.scrollHeight
      }
    }
  },
  mounted() {
    // 组件挂载后滚动到底部
    this.$nextTick(() => {
      this.scrollToBottom()
    })
  }
}
</script>
