<template>
  <div class="chat-modal-overlay" @click="closeModal">
    <div class="chat-modal" @click.stop>
      <!-- 弹窗头部 -->
      <div class="chat-header">
        <h3>智能助手</h3>
        <button class="close-btn" @click="closeModal">×</button>
      </div>

      <!-- 弹窗内容 -->
      <div class="chat-content">
        <!-- 左侧：数字人形象区域 -->
        <div class="left-panel">
          <div class="digital-avatar-full">
            <!-- 默认显示图片，充满整个区域 -->
            <img
              v-if="!isTyping"
              src="/1.png"
              alt="数字人形象"
              class="avatar-full-image"
            />
            <!-- 数字人回复时显示视频，充满整个区域 -->
            <video
              v-else
              ref="avatarVideo"
              src="/1.mp4"
              class="avatar-full-video"
              autoplay
              muted
              @ended="onVideoEnded"
            ></video>
          </div>
        </div>

        <!-- 右侧：对话区域 -->
        <div class="right-panel">
          <!-- 对话消息区域 -->
          <div class="chat-messages" ref="messagesContainer">
            <div
              v-for="message in messages"
              :key="message.id"
              :class="['message', message.type]"
            >
              <div class="message-bubble">
                {{ message.content }}
              </div>
            </div>
          </div>

          <!-- 输入区域 -->
          <div class="chat-input-area">
            <input
              v-model="inputMessage"
              @keyup.enter="sendMessage"
              class="chat-input"
              :class="{ 'input-disabled': isTyping }"
              placeholder="请输入您的问题..."
              :disabled="isTyping"
            />
            <button
              @click="sendMessage"
              class="send-btn"
              :disabled="!inputMessage.trim() || isTyping"
            >
              ➤
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ChatModal',
  emits: ['close'],
  data() {
    return {
      inputMessage: '',
      isTyping: false,
      messages: [
        {
          id: 1,
          type: 'assistant',
          content: '您好！我是您的智能助手小智，很高兴为您服务！请问有什么可以帮助您的吗？'
        }
      ]
    }
  },
  methods: {
    closeModal() {
      this.$emit('close')
    },
    async sendMessage() {
      if (!this.inputMessage.trim() || this.isTyping) return

      // 添加用户消息
      const userMessage = {
        id: Date.now(),
        type: 'user',
        content: this.inputMessage.trim()
      }
      this.messages.push(userMessage)

      // 清空输入框
      const userInput = this.inputMessage
      this.inputMessage = ''
      this.isTyping = true

      // 滚动到底部
      this.$nextTick(() => {
        this.scrollToBottom()
      })

      // 模拟数字人思考时间
      setTimeout(() => {
        // 添加数字人回复
        const assistantMessage = {
          id: Date.now() + 1,
          type: 'assistant',
          content: `我收到了您的问题："${userInput}"。这是一个很好的问题，我正在为您分析和处理中...`
        }
        this.messages.push(assistantMessage)

        // 滚动到底部
        this.$nextTick(() => {
          this.scrollToBottom()
        })
      }, 1500)
    },
    onVideoEnded() {
      // 视频播放完毕，恢复输入框可编辑状态
      this.isTyping = false
    },

    scrollToBottom() {
      const container = this.$refs.messagesContainer
      if (container) {
        container.scrollTop = container.scrollHeight
      }
    }
  },
  mounted() {
    // 组件挂载后滚动到底部
    this.$nextTick(() => {
      this.scrollToBottom()
    })
  }
}
</script>

<style scoped>
.chat-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.chat-modal {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  width: 800px;
  height: 600px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.chat-header h3 {
  color: white;
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.3s;
}

.close-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.chat-content {
  flex: 1;
  display: flex;
  flex-direction: row;
  background: white;
  margin: 0 20px 20px 20px;
  border-radius: 15px;
  overflow: hidden;
}

/* 左侧面板 - 数字人区域 */
.left-panel {
  width: 50%;
  display: flex;
  flex-direction: column;
}

/* 右侧面板 - 对话区域 */
.right-panel {
  width: 50%;
  display: flex;
  flex-direction: column;
  border-left: 1px solid #e0e0e0;
}

/* 数字人形象区域 - 充满左侧面板 */
.digital-avatar-full {
  position: relative;
  height: 100%;
  overflow: hidden;
  background: #f5f7fa;
}

.avatar-full-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center top; /* 从顶部开始显示，确保显示头部和上半身 */
}

.avatar-full-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center 32%; /* 视频稍微往下移动，确保头部完整显示 */
}

/* 名称和状态覆盖层 */
.avatar-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  color: white;
  padding: 20px;
  text-align: center;
}

.avatar-name {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 5px;
}

.avatar-status {
  font-size: 14px;
  opacity: 0.9;
}

.chat-messages {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  max-height: 200px;
}

.message {
  margin-bottom: 15px;
  display: flex;
}

.message.user {
  justify-content: flex-end;
}

.message.assistant {
  justify-content: flex-start;
}

.message-bubble {
  max-width: 80%;
  padding: 12px 16px;
  border-radius: 18px;
  font-size: 14px;
  line-height: 1.4;
}

.message.user .message-bubble {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.message.assistant .message-bubble {
  background: #f0f0f0;
  color: #333;
}

.chat-input-area {
  display: flex;
  padding: 20px;
  gap: 10px;
  border-top: 1px solid #e0e0e0;
  background: #fafafa;
}

.chat-input {
  flex: 1;
  padding: 12px 16px;
  border: 2px solid #e0e0e0;
  border-radius: 25px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.3s, background-color 0.3s;
}

.chat-input:focus {
  border-color: #667eea;
}

.chat-input:disabled,
.chat-input.input-disabled {
  background-color: #f0f0f0;
  color: #999;
  cursor: not-allowed;
  border-color: #ddd;
}

.send-btn {
  width: 45px;
  height: 45px;
  border: none;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-size: 18px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s, box-shadow 0.2s;
}

.send-btn:hover:not(:disabled) {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.send-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 滚动条样式 */
.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
