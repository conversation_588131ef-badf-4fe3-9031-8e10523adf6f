<template>
  <div class="card metric-card">
    <div class="metric-label">{{ metric.label }}</div>
    <div class="metric-value" :style="{ color: getValueColor() }">
      {{ metric.value }}
    </div>
    <div class="metric-change" :class="metric.trend">
      {{ metric.change }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'MetricCard',
  props: {
    metric: {
      type: Object,
      required: true
    }
  },
  methods: {
    getValueColor() {
      const colors = ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe']
      return colors[this.metric.id % colors.length]
    }
  }
}
</script>
