<template>
  <div class="digital-assistant">
    <!-- 触发按钮 -->
    <button 
      class="assistant-trigger" 
      @click="openChat"
      :title="isOpen ? '关闭助手' : '打开智能助手'"
    >
      🤖
    </button>

    <!-- 对话弹窗 -->
    <ChatModal 
      v-if="isOpen" 
      @close="closeChat"
    />
  </div>
</template>

<script>
import ChatModal from './ChatModal.vue'

export default {
  name: 'DigitalAssistant',
  components: {
    ChatModal
  },
  data() {
    return {
      isOpen: false
    }
  },
  methods: {
    openChat() {
      this.isOpen = true
      // 防止页面滚动
      document.body.style.overflow = 'hidden'
    },
    closeChat() {
      this.isOpen = false
      // 恢复页面滚动
      document.body.style.overflow = 'auto'
    }
  },
  beforeUnmount() {
    // 组件销毁时恢复页面滚动
    document.body.style.overflow = 'auto'
  }
}
</script>
