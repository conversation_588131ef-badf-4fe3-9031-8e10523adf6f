// 模拟数据
export const metrics = [
  {
    id: 1,
    label: '总销售额',
    value: '¥2,847,392',
    change: '+12.5%',
    trend: 'positive'
  },
  {
    id: 2,
    label: '活跃用户',
    value: '18,429',
    change: '+8.2%',
    trend: 'positive'
  },
  {
    id: 3,
    label: '订单数量',
    value: '3,247',
    change: '-2.1%',
    trend: 'negative'
  },
  {
    id: 4,
    label: '转化率',
    value: '3.24%',
    change: '+0.8%',
    trend: 'positive'
  }
]

export const salesData = {
  labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
  datasets: [{
    label: '销售额 (万元)',
    data: [120, 190, 300, 500, 420, 380, 450, 520, 480, 600, 550, 680],
    borderColor: 'rgb(75, 192, 192)',
    backgroundColor: 'rgba(75, 192, 192, 0.2)',
    tension: 0.4,
    fill: true
  }, {
    label: '目标销售额 (万元)',
    data: [150, 200, 280, 450, 400, 350, 420, 500, 460, 580, 530, 650],
    borderColor: 'rgb(255, 99, 132)',
    backgroundColor: 'rgba(255, 99, 132, 0.1)',
    tension: 0.4,
    borderDash: [5, 5],
    fill: false
  }]
}

export const userGrowthData = {
  labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
  datasets: [{
    label: '新增用户',
    data: [1200, 1900, 3000, 5000, 4200, 3800, 4500, 5200, 4800, 6000, 5500, 6800],
    backgroundColor: 'rgba(54, 162, 235, 0.8)',
    borderColor: 'rgba(54, 162, 235, 1)',
    borderWidth: 2
  }, {
    label: '活跃用户',
    data: [8500, 9200, 11000, 14500, 13800, 13200, 14800, 16200, 15800, 18000, 17200, 19500],
    backgroundColor: 'rgba(255, 99, 132, 0.8)',
    borderColor: 'rgba(255, 99, 132, 1)',
    borderWidth: 2
  }]
}

export const categoryData = {
  labels: ['电子产品', '服装配饰', '家居用品', '图书文具', '运动健身', '美妆护肤', '食品饮料'],
  datasets: [{
    label: '销售占比 (%)',
    data: [35.2, 18.5, 15.3, 12.8, 8.7, 6.2, 3.3],
    backgroundColor: [
      '#FF6384',
      '#36A2EB',
      '#FFCE56',
      '#4BC0C0',
      '#9966FF',
      '#FF9F40',
      '#FF6384'
    ],
    borderColor: [
      '#FF6384',
      '#36A2EB',
      '#FFCE56',
      '#4BC0C0',
      '#9966FF',
      '#FF9F40',
      '#FF6384'
    ],
    borderWidth: 2,
    hoverOffset: 4
  }]
}

export const recentOrders = [
  {
    id: 'ORD-001',
    customer: '张三',
    product: 'iPhone 15 Pro',
    amount: '¥8,999',
    status: 'active',
    date: '2024-01-15'
  },
  {
    id: 'ORD-002',
    customer: '李四',
    product: '小米13',
    amount: '¥3,999',
    status: 'pending',
    date: '2024-01-14'
  },
  {
    id: 'ORD-003',
    customer: '王五',
    product: 'MacBook Pro',
    amount: '¥16,999',
    status: 'active',
    date: '2024-01-14'
  },
  {
    id: 'ORD-004',
    customer: '赵六',
    product: 'iPad Air',
    amount: '¥4,599',
    status: 'inactive',
    date: '2024-01-13'
  },
  {
    id: 'ORD-005',
    customer: '钱七',
    product: 'AirPods Pro',
    amount: '¥1,899',
    status: 'active',
    date: '2024-01-13'
  },
  {
    id: 'ORD-006',
    customer: '孙八',
    product: '华为Mate60',
    amount: '¥5,999',
    status: 'active',
    date: '2024-01-12'
  },
  {
    id: 'ORD-007',
    customer: '周九',
    product: 'Nike运动鞋',
    amount: '¥899',
    status: 'pending',
    date: '2024-01-12'
  },
  {
    id: 'ORD-008',
    customer: '吴十',
    product: '戴森吸尘器',
    amount: '¥2,999',
    status: 'active',
    date: '2024-01-11'
  }
]
