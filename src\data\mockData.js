// 模拟数据
export const metrics = [
  {
    id: 1,
    label: '总销售额',
    value: '¥2,847,392',
    change: '+12.5%',
    trend: 'positive'
  },
  {
    id: 2,
    label: '活跃用户',
    value: '18,429',
    change: '+8.2%',
    trend: 'positive'
  },
  {
    id: 3,
    label: '订单数量',
    value: '3,247',
    change: '-2.1%',
    trend: 'negative'
  },
  {
    id: 4,
    label: '转化率',
    value: '3.24%',
    change: '+0.8%',
    trend: 'positive'
  }
]

export const salesData = {
  labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
  datasets: [{
    label: '销售额 (万元)',
    data: [120, 190, 300, 500, 200, 300],
    borderColor: 'rgb(75, 192, 192)',
    backgroundColor: 'rgba(75, 192, 192, 0.2)',
    tension: 0.4
  }]
}

export const userGrowthData = {
  labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
  datasets: [{
    label: '新增用户',
    data: [1200, 1900, 3000, 5000, 2000, 3000],
    backgroundColor: [
      'rgba(255, 99, 132, 0.8)',
      'rgba(54, 162, 235, 0.8)',
      'rgba(255, 205, 86, 0.8)',
      'rgba(75, 192, 192, 0.8)',
      'rgba(153, 102, 255, 0.8)',
      'rgba(255, 159, 64, 0.8)'
    ]
  }]
}

export const categoryData = {
  labels: ['电子产品', '服装', '家居', '图书', '运动'],
  datasets: [{
    data: [300, 150, 100, 80, 70],
    backgroundColor: [
      '#FF6384',
      '#36A2EB',
      '#FFCE56',
      '#4BC0C0',
      '#9966FF'
    ]
  }]
}

export const recentOrders = [
  {
    id: 'ORD-001',
    customer: '张三',
    product: 'iPhone 15 Pro',
    amount: '¥8,999',
    status: 'active',
    date: '2024-01-15'
  },
  {
    id: 'ORD-002',
    customer: '李四',
    product: '小米13',
    amount: '¥3,999',
    status: 'pending',
    date: '2024-01-14'
  },
  {
    id: 'ORD-003',
    customer: '王五',
    product: 'MacBook Pro',
    amount: '¥16,999',
    status: 'active',
    date: '2024-01-14'
  },
  {
    id: 'ORD-004',
    customer: '赵六',
    product: 'iPad Air',
    amount: '¥4,599',
    status: 'inactive',
    date: '2024-01-13'
  },
  {
    id: 'ORD-005',
    customer: '钱七',
    product: 'AirPods Pro',
    amount: '¥1,899',
    status: 'active',
    date: '2024-01-13'
  }
]
