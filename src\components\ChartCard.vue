<template>
  <div class="card">
    <h3>{{ title }}</h3>
    <div class="chart-container">
      <canvas :ref="chartRef"></canvas>
    </div>
  </div>
</template>

<script>
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
} from 'chart.js'

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
)

export default {
  name: 'ChartCard',
  props: {
    title: {
      type: String,
      required: true
    },
    type: {
      type: String,
      required: true
    },
    data: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      chart: null,
      chartRef: `chart-${Math.random().toString(36).substr(2, 9)}`
    }
  },
  mounted() {
    this.createChart()
  },
  beforeUnmount() {
    if (this.chart) {
      this.chart.destroy()
    }
  },
  methods: {
    createChart() {
      const ctx = this.$refs[this.chartRef].getContext('2d')
      
      const options = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'top',
          },
          title: {
            display: false
          }
        }
      }

      if (this.type === 'line') {
        options.scales = {
          y: {
            beginAtZero: true
          }
        }
      } else if (this.type === 'bar') {
        options.scales = {
          y: {
            beginAtZero: true
          }
        }
      }

      this.chart = new ChartJS(ctx, {
        type: this.type,
        data: this.data,
        options
      })
    }
  }
}
</script>
