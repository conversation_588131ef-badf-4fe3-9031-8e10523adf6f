<template>
  <div class="dashboard">
    <!-- 头部 -->
    <div class="dashboard-header">
      <h1>数据驾驶舱</h1>
      <p>实时监控业务关键指标</p>
    </div>

    <!-- 指标卡片 -->
    <div class="metrics-grid">
      <MetricCard 
        v-for="metric in metrics" 
        :key="metric.id" 
        :metric="metric" 
      />
    </div>

    <!-- 图表区域 -->
    <div class="charts-grid">
      <ChartCard 
        title="销售趋势" 
        type="line" 
        :data="salesData" 
      />
      <ChartCard 
        title="用户增长" 
        type="bar" 
        :data="userGrowthData" 
      />
      <ChartCard 
        title="产品分类占比" 
        type="doughnut" 
        :data="categoryData" 
      />
    </div>

    <!-- 数据表格 -->
    <DataTable 
      title="最近订单" 
      :orders="recentOrders" 
    />
  </div>
</template>

<script>
import MetricCard from './MetricCard.vue'
import ChartCard from './ChartCard.vue'
import DataTable from './DataTable.vue'
import { metrics, salesData, userGrowthData, categoryData, recentOrders } from '../data/mockData.js'

export default {
  name: 'Dashboard',
  components: {
    MetricCard,
    ChartCard,
    DataTable
  },
  data() {
    return {
      metrics,
      salesData,
      userGrowthData,
      categoryData,
      recentOrders
    }
  }
}
</script>
