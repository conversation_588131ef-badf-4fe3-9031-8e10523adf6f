<template>
  <div class="table-section">
    <h3>{{ title }}</h3>
    <table class="table">
      <thead>
        <tr>
          <th>订单号</th>
          <th>客户</th>
          <th>产品</th>
          <th>金额</th>
          <th>状态</th>
          <th>日期</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="order in orders" :key="order.id">
          <td>{{ order.id }}</td>
          <td>{{ order.customer }}</td>
          <td>{{ order.product }}</td>
          <td>{{ order.amount }}</td>
          <td>
            <span 
              class="status-badge" 
              :class="getStatusClass(order.status)"
            >
              {{ getStatusText(order.status) }}
            </span>
          </td>
          <td>{{ order.date }}</td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script>
export default {
  name: 'DataTable',
  props: {
    title: {
      type: String,
      required: true
    },
    orders: {
      type: Array,
      required: true
    }
  },
  methods: {
    getStatusClass(status) {
      return `status-${status}`
    },
    getStatusText(status) {
      const statusMap = {
        active: '已完成',
        pending: '处理中',
        inactive: '已取消'
      }
      return statusMap[status] || status
    }
  }
}
</script>
