{"name": "vue-chartjs", "type": "module", "version": "5.3.2", "description": "Vue.js wrapper for chart.js for creating beautiful charts.", "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "homepage": "http://vue-chartjs.org", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "web": "https://github.com/LinusBorg"}, {"name": "<PERSON>", "web": "https://github.com/jcalonso"}], "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "web": "http://www.jakubjuszczak.de"}], "repository": {"type": "git", "url": "git+ssh://**************:apertureless/vue-chartjs.git"}, "bugs": {"url": "https://github.com/apertureless/vue-chartjs/issues"}, "keywords": ["ChartJs", "<PERSON><PERSON>", "Visualisation", "Wrapper", "Charts"], "sideEffects": false, "types": "./dist/index.d.ts", "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}, "publishConfig": {"directory": "package"}, "files": ["dist"], "peerDependencies": {"chart.js": "^4.1.1", "vue": "^3.0.0-0 || ^2.7.0"}, "main": "./dist/index.cjs", "module": "./dist/index.js", "scripts": {}}